/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * 表格列设置组件
 *
 * @param {Array} columns - 原始列配置
 * @param {string} storageKey - 本地存储的键名
 * @param {Function} onColumnsChange - 列变化回调，接收 {rawSettings, processedColumns} 对象
 *   - rawSettings: 原始设置数据，用于存储
 *   - processedColumns: 处理后可直接用于表格的列数据
 * @param {ReactNode} trigger - 触发抽屉的自定义元素
 */

import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import { message, Drawer, Row, Col, Icon, Button, Checkbox, List } from 'antd';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { setLStorage, getLStorage } from '@/utils/utils';
import styles from './ColumnsSetting.less';

const TYPE = 'DraggableListItem';

/**
 * 表格列设置组件
 *
 * @param {Array} columns - 原始列配置
 * @param {string} storageKey - 本地存储的键名
 * @param {Function} onColumnsChange - 列变化回调，接收 {rawSettings, processedColumns} 对象
 *   - rawSettings: 原始设置数据，用于存储
 *   - processedColumns: 处理后可直接用于表格的列数据
 * @param {ReactNode} trigger - 触发抽屉的自定义元素
 */

// 拖拽列表项组件
const DraggableListItem = ({ index, moveItem, children, ...props }) => {
  const ref = useRef();
  const [{ handlerId }, drop] = useDrop({
    accept: TYPE,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      if (dragIndex === hoverIndex) {
        return;
      }

      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      moveItem(dragIndex, hoverIndex);
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: TYPE,
    item: () => {
      return { index };
    },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const opacity = isDragging ? 0.4 : 1;
  drag(drop(ref));

  return (
    <div ref={ref} style={{ opacity }} data-handler-id={handlerId} {...props}>
      {children}
    </div>
  );
};

const ColumnsSetting = ({
  columns = [],
  storageKey,
  onColumnsChange, // 列变化回调，将同时提供原始设置和处理后的列
  trigger,
}) => {
  const [visible, setVisible] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);

  // 初始化表格数据
  const initTableData = useCallback(
    () => {
      const savedData = getLStorage(storageKey);

      if (savedData && Array.isArray(savedData)) {
        // 使用保存的配置，但需要与当前columns同步
        // 合并保存的配置和当前columns
        const mergedData = [];
        const processedKeys = new Set();

        // 先添加保存的配置中存在的列
        savedData.forEach(savedItem => {
          const currentColumn = columns.find(col => (col.key || col.dataIndex) === savedItem.key);
          if (currentColumn) {
            mergedData.push({
              ...savedItem,
              title: currentColumn.title, // 使用最新的title
              fixed: savedItem.fixed || currentColumn.fixed || false, // 保持固定设置
            });
            processedKeys.add(savedItem.key);
          }
        });

        // 再添加新增的列
        columns.forEach(col => {
          const key = col.key || col.dataIndex;
          if (!processedKeys.has(key)) {
            mergedData.push({
              key,
              title: col.title,
              selected: true, // 新列默认显示
              fixed: col.fixed || false, // 使用列的默认固定设置
            });
          }
        });

        return mergedData;
      }
      // 首次使用，基于当前columns创建配置
      return columns.map(col => ({
        key: col.key || col.dataIndex,
        title: col.title,
        selected: true,
        fixed: col.fixed || false, // 使用列的默认固定设置
      }));
    },
    [columns, storageKey],
  );

  // 保存配置
  const handleSave = useCallback(
    () => {
      setLStorage(storageKey, tableData);
      if (onColumnsChange) {
        // 同时提供原始设置和处理后的列
        onColumnsChange({
          rawSettings: tableData,
          processedColumns,
        });
      }
      setVisible(false);
      message.success('设置成功！');
    },
    [tableData, storageKey, onColumnsChange, processedColumns],
  );

  // 处理选择变化
  const handleSelectRows = useCallback(
    (selected = []) => {
      const newTableData = tableData.map(item => ({
        ...item,
        selected: selected.includes(item.key),
      }));
      setTableData(newTableData);
      setSelectedRowKeys(selected);
    },
    [tableData],
  );

  // 辅助函数：基于原始设置生成处理后的列
  const generateProcessedColumns = useCallback(
    settings => {
      if (!settings || !settings.length) return columns;

      const columnMap = new Map();
      settings.forEach((col, index) => {
        columnMap.set(col.key, { ...col, order: index });
      });

      const processedCols = columns
        .map(col => {
          const key = col.key || col.dataIndex;
          const setting = columnMap.get(key);
          return setting
            ? {
                ...col,
                order: setting.order,
                visible: setting.selected,
                fixed: setting.fixed !== undefined ? setting.fixed : col.fixed || false,
                sortOrder: setting.sortOrder,
              }
            : { ...col, visible: true, order: 999, fixed: col.fixed || false };
        })
        .filter(col => col.visible);

      // 按照 left -> normal -> right 的顺序排序
      const leftCols = processedCols.filter(col => col.fixed === 'left');
      const normalCols = processedCols.filter(col => !col.fixed || col.fixed === false);
      const rightCols = processedCols.filter(col => col.fixed === 'right');

      const sortByOrder = cols =>
        cols.sort((a, b) => {
          if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
            return a.sortOrder - b.sortOrder;
          }
          return (a.order || 0) - (b.order || 0);
        });

      return [...sortByOrder(leftCols), ...sortByOrder(normalCols), ...sortByOrder(rightCols)];
    },
    [columns],
  );

  // 拖拽移动项目
  const moveItem = useCallback(
    (dragIndex, hoverIndex) => {
      const sortedData = [...sortedTableData];
      const dragItem = sortedData[dragIndex];
      sortedData.splice(dragIndex, 1);
      sortedData.splice(hoverIndex, 0, dragItem);

      // 重新构建tableData，保持原有顺序但应用拖拽结果
      const newTableData = [...tableData];
      sortedData.forEach((item, index) => {
        const originalIndex = newTableData.findIndex(orig => orig.key === item.key);
        if (originalIndex !== -1) {
          newTableData[originalIndex] = { ...item, sortOrder: index };
        }
      });

      setTableData(newTableData);
    },
    [tableData, sortedTableData],
  );

  // 切换到固定左侧
  const moveToLeft = useCallback(
    itemKey => {
      const newData = [...tableData];
      const targetIndex = newData.findIndex(item => item.key === itemKey);
      if (targetIndex !== -1) {
        newData[targetIndex] = { ...newData[targetIndex], fixed: 'left' };
        setTableData(newData);
      }
    },
    [tableData],
  );

  // 切换到固定右侧
  const moveToRight = useCallback(
    itemKey => {
      const newData = [...tableData];
      const targetIndex = newData.findIndex(item => item.key === itemKey);
      if (targetIndex !== -1) {
        newData[targetIndex] = { ...newData[targetIndex], fixed: 'right' };
        setTableData(newData);
      }
    },
    [tableData],
  );

  // 取消固定
  const moveToNormal = useCallback(
    itemKey => {
      const newData = [...tableData];
      const targetIndex = newData.findIndex(item => item.key === itemKey);
      if (targetIndex !== -1) {
        newData[targetIndex] = { ...newData[targetIndex], fixed: false };
        setTableData(newData);
      }
    },
    [tableData],
  );

  // 按fixed排序的数据
  const sortedTableData = useMemo(
    () => {
      const leftItems = tableData.filter(item => item.fixed === 'left');
      const normalItems = tableData.filter(item => !item.fixed || item.fixed === false);
      const rightItems = tableData.filter(item => item.fixed === 'right');

      // 如果有sortOrder，按sortOrder排序，否则保持原顺序
      const sortByOrder = items => {
        return items.sort((a, b) => {
          if (a.sortOrder !== undefined && b.sortOrder !== undefined) {
            return a.sortOrder - b.sortOrder;
          }
          return 0;
        });
      };

      return [...sortByOrder(leftItems), ...sortByOrder(normalItems), ...sortByOrder(rightItems)];
    },
    [tableData],
  );

  // 处理后的列数据 - 供外部直接使用
  const processedColumns = useMemo(() => generateProcessedColumns(tableData), [
    tableData,
    generateProcessedColumns,
  ]);

  // 全选/取消全选
  const handleSelectAll = useCallback(
    checked => {
      const allKeys = checked ? tableData.map(item => item.key) : [];
      handleSelectRows(allKeys);
    },
    [tableData, handleSelectRows],
  );

  // 重置为默认
  const handleReset = useCallback(
    () => {
      const defaultData = columns.map(col => ({
        key: col.key || col.dataIndex,
        title: col.title,
        selected: true,
      }));
      setTableData(defaultData);
      setSelectedRowKeys(defaultData.map(item => item.key));
    },
    [columns],
  );

  // 组件挂载时初始化数据并通知外部
  useEffect(
    () => {
      const data = initTableData();
      setTableData(data);
      setSelectedRowKeys(data.filter(item => item.selected).map(item => item.key));

      // 如果有保存的配置，立即通知外部应用
      const savedData = getLStorage(storageKey);
      if (savedData && Array.isArray(savedData) && onColumnsChange) {
        // 同时提供原始设置和通过原始设置生成的处理后列数据
        const initialProcessedColumns = generateProcessedColumns(savedData);
        onColumnsChange({
          rawSettings: savedData,
          processedColumns: initialProcessedColumns,
        });
      }
    },
    [initTableData, storageKey, onColumnsChange],
  );

  // 当抽屉打开时，重新同步数据
  useEffect(
    () => {
      if (visible) {
        const data = initTableData();
        setTableData(data);
        setSelectedRowKeys(data.filter(item => item.selected).map(item => item.key));
      }
    },
    [visible, initTableData],
  );

  const isAllSelected = selectedRowKeys.length === tableData.length;
  const isIndeterminate = selectedRowKeys.length > 0 && selectedRowKeys.length < tableData.length;

  return (
    <>
      {trigger ? (
        React.cloneElement(trigger, { onClick: () => setVisible(true) })
      ) : (
        <Button onClick={() => setVisible(true)}>
          <Icon type="setting" />
          设置表头
        </Button>
      )}

      <Drawer
        title="列显示设置"
        placement="right"
        closable
        onClose={() => setVisible(false)}
        visible={visible}
        width={400}
        destroyOnClose
        className={styles.columnsDrawer}
      >
        <div className={styles.header}>
          <Row type="flex" justify="space-between" align="middle">
            <Col>
              <Checkbox
                indeterminate={isIndeterminate}
                checked={isAllSelected}
                onChange={e => handleSelectAll(e.target.checked)}
              >
                列显示
              </Checkbox>
            </Col>
            <Col>
              <Button type="link" size="small" onClick={handleReset}>
                重置
              </Button>
            </Col>
          </Row>
        </div>

        <div className={styles.content}>
          <DndProvider backend={HTML5Backend}>
            {/* 固定左侧组 */}
            {sortedTableData.filter(item => item.fixed === 'left').length > 0 && (
              <div className={styles.group}>
                <div className={styles.groupTitle}>
                  <div className={styles['title-text']}>
                    <Icon type="pushpin" style={{ marginRight: 4 }} />
                    固定左侧
                  </div>
                </div>
                <List
                  dataSource={sortedTableData.filter(item => item.fixed === 'left')}
                  renderItem={item => {
                    const globalIndex = sortedTableData.findIndex(data => data.key === item.key);
                    return (
                      <DraggableListItem
                        key={item.key}
                        index={globalIndex}
                        moveItem={moveItem}
                        className={styles.listItem}
                      >
                        <List.Item>
                          <Row type="flex" justify="space-between" align="middle" style={{ width: '100%' }}>
                            <Col>
                              <Row type="flex" align="middle">
                                <Col>
                                  <Checkbox
                                    checked={selectedRowKeys.includes(item.key)}
                                    onChange={e => {
                                      const { checked } = e.target;
                                      const newSelected = checked
                                        ? [...selectedRowKeys, item.key]
                                        : selectedRowKeys.filter(key => key !== item.key);
                                      handleSelectRows(newSelected);
                                    }}
                                  />
                                </Col>
                                <Col style={{ marginLeft: 8 }}>
                                  <span>{item.title}</span>
                                </Col>
                              </Row>
                            </Col>

                            <Col>
                              <Button.Group size="small" className={styles['operation-buttons']}>
                                <Button
                                  icon="arrow-down"
                                  title="固定到右侧"
                                  onClick={() => moveToRight(item.key)}
                                />
                                <Button
                                  icon="undo"
                                  title="取消固定"
                                  onClick={() => moveToNormal(item.key)}
                                />
                              </Button.Group>
                            </Col>
                          </Row>
                        </List.Item>
                      </DraggableListItem>
                    );
                  }}
                />
              </div>
            )}

            {/* 不固定组 */}
            {sortedTableData.filter(item => !item.fixed || item.fixed === false).length > 0 && (
              <div className={styles.group}>
                <div className={styles.groupTitle}>
                  <div className={styles['title-text']}>
                    <Icon type="menu" style={{ marginRight: 4 }} />
                    不固定
                  </div>
                </div>
                <List
                  dataSource={sortedTableData.filter(item => !item.fixed || item.fixed === false)}
                  renderItem={item => {
                    const globalIndex = sortedTableData.findIndex(data => data.key === item.key);
                    return (
                      <DraggableListItem
                        key={item.key}
                        index={globalIndex}
                        moveItem={moveItem}
                        className={styles.listItem}
                      >
                        <List.Item>
                          <Row type="flex" justify="space-between" align="middle" style={{ width: '100%' }}>
                            <Col>
                              <Row type="flex" align="middle">
                                <Col>
                                  <Checkbox
                                    checked={selectedRowKeys.includes(item.key)}
                                    onChange={e => {
                                      const { checked } = e.target;
                                      const newSelected = checked
                                        ? [...selectedRowKeys, item.key]
                                        : selectedRowKeys.filter(key => key !== item.key);
                                      handleSelectRows(newSelected);
                                    }}
                                  />
                                </Col>
                                <Col style={{ marginLeft: 8 }}>
                                  <span>{item.title}</span>
                                </Col>
                              </Row>
                            </Col>
                            <Col>
                              <Button.Group size="small" className={styles['operation-buttons']}>
                                <Button
                                  icon="arrow-up"
                                  title="固定到左侧"
                                  onClick={() => moveToLeft(item.key)}
                                />
                                <Button
                                  icon="arrow-down"
                                  title="固定到右侧"
                                  onClick={() => moveToRight(item.key)}
                                />
                              </Button.Group>
                            </Col>
                          </Row>
                        </List.Item>
                      </DraggableListItem>
                    );
                  }}
                />
              </div>
            )}

            {/* 固定右侧组 */}
            {sortedTableData.filter(item => item.fixed === 'right').length > 0 && (
              <div className={styles.group}>
                <div className={styles.groupTitle}>
                  <div className={styles['title-text']}>
                    <Icon type="pushpin" style={{ marginRight: 4, transform: 'scaleX(-1)' }} />
                    固定右侧
                  </div>
                </div>
                <List
                  dataSource={sortedTableData.filter(item => item.fixed === 'right')}
                  renderItem={item => {
                    const globalIndex = sortedTableData.findIndex(data => data.key === item.key);
                    return (
                      <DraggableListItem
                        key={item.key}
                        index={globalIndex}
                        moveItem={moveItem}
                        className={styles.listItem}
                      >
                        <List.Item>
                          <Row type="flex" justify="space-between" align="middle" style={{ width: '100%' }}>
                            <Col>
                              <Row type="flex" align="middle">
                                <Col>
                                  <Checkbox
                                    checked={selectedRowKeys.includes(item.key)}
                                    onChange={e => {
                                      const { checked } = e.target;
                                      const newSelected = checked
                                        ? [...selectedRowKeys, item.key]
                                        : selectedRowKeys.filter(key => key !== item.key);
                                      handleSelectRows(newSelected);
                                    }}
                                  />
                                </Col>
                                <Col style={{ marginLeft: 8 }}>
                                  <span>{item.title}</span>
                                </Col>
                              </Row>
                            </Col>
                            <Col>
                              <Button.Group size="small" className={styles['operation-buttons']}>
                                <Button
                                  icon="arrow-up"
                                  title="固定到左侧"
                                  onClick={() => moveToLeft(item.key)}
                                />
                                <Button
                                  icon="undo"
                                  title="取消固定"
                                  onClick={() => moveToNormal(item.key)}
                                />
                              </Button.Group>
                            </Col>
                          </Row>
                        </List.Item>
                      </DraggableListItem>
                    );
                  }}
                />
              </div>
            )}
          </DndProvider>
        </div>

        <div className={styles.footer}>
          <Row type="flex" justify="end" gutter={8}>
            <Col>
              <Button onClick={() => setVisible(false)}>取消</Button>
            </Col>
            <Col>
              <Button type="primary" onClick={handleSave}>
                确定
              </Button>
            </Col>
          </Row>
        </div>
      </Drawer>
    </>
  );
};

export default ColumnsSetting;
